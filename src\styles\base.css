/* Base Styles - Converted from JavaScript objects to proper CSS */

/* Reset and base styles */
*, *::before, *::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background-color: #e2e8f0;
  color: #2d3748;
  line-height: 1.6;
  min-height: 100vh;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100vh;
}

/* Container styles */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  position: relative;
}

/* Welcome page styles */
.welcome-text {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  color: #2d3748;
  margin-bottom: 3rem;
  max-width: 800px;
  line-height: 1.3;
  opacity: 0;
  transform: translateY(20px);
}

.welcome-text.visible {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.5s ease-out;
}

/* Microphone button styles */
.microphone-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #4299e1;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  opacity: 0;
  transform: translateY(20px);
  view-transition-name: microphone-button;
}

.microphone-button.visible {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.5s ease-out;
  transition-delay: 0.2s;
}

.microphone-button:hover {
  background-color: #3182ce;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
}

/* Test page styles */
.test-text {
  font-size: 1.8rem;
  font-weight: 500;
  text-align: center;
  color: #2d3748;
  max-width: 1200px;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 1;
}

/* Floating microphone styles */
.floating-microphone {
  position: fixed;
  bottom: 300px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #4299e1;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  opacity: 1;
  view-transition-name: microphone-button;
  transition: all 0.3s ease;
  z-index: 1000;
}

.floating-microphone:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
  opacity: 0.6;
}

.floating-microphone.needs-permission {
  background-color: #ed8936;
  cursor: pointer;
  opacity: 1;
}

.floating-microphone.needs-permission:hover {
  background-color: #dd6b20;
  transform: translateX(-50%) scale(1.05);
}

.floating-microphone.available:hover {
  background-color: #3182ce;
  transform: translateX(-50%) scale(1.05);
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
}

.floating-microphone.recording {
  background-color: #e53e3e;
  animation: pulse-recording 1.5s infinite;
}

.floating-microphone.recording:hover {
  background-color: #c53030;
}

@keyframes pulse-recording {
  0% {
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
    transform: translateX(-50%);
  }
  50% {
    box-shadow: 0 4px 20px rgba(229, 62, 62, 0.6);
    transform: translateX(-50%) scale(1.02);
  }
  100% {
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
    transform: translateX(-50%);
  }
}

/* Microphone icon styles */
.microphone-icon {
  width: 32px;
  height: 32px;
  fill: currentColor;
}

/* Recording status and duration */
.recording-status {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: #2d3748;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.recording-status.recording {
  background-color: #e53e3e;
}

.recording-status.success {
  background-color: #38a169;
}

.recording-duration {
  position: fixed;
  top: 5rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(45, 55, 72, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Error message */
.error-message {
  position: fixed;
  bottom: 8rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fed7d7;
  color: #c53030;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #feb2b2;
  font-size: 0.9rem;
  font-weight: 500;
  z-index: 1000;
  max-width: 90%;
  text-align: center;
  box-shadow: 0 4px 12px rgba(197, 48, 48, 0.15);
}

/* Button styles */
.btn {
  padding: 0.75rem 1.5rem;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  background-color: #3182ce;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.btn.btn-outline {
  background-color: transparent;
  color: #4299e1;
  border: 1px solid #4299e1;
}

.btn.btn-outline:hover {
  background-color: #4299e1;
  color: white;
}

/* Back button styles */
.back-button {
  position: absolute;
  top: 2rem;
  left: 2rem;
  z-index: 1000;
}

/* Demo link styles */
.demo-link {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  color: #4299e1;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border: 1px solid #4299e1;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.demo-link:hover {
  background-color: #4299e1;
  color: white;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.fade-out {
  animation: fadeOut 0.5s ease-out forwards;
}

.slide-up {
  animation: slideUp 0.5s ease-out forwards;
}

.slide-down {
  animation: slideDown 0.5s ease-out forwards;
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .welcome-text {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .test-text {
    font-size: 1.5rem;
  }
  
  .container {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .welcome-text {
    font-size: 1.5rem;
  }
  
  .test-text {
    font-size: 1.2rem;
  }
}
