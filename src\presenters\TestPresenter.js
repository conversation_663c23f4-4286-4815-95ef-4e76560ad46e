import TestView from '../views/TestView.js';
import AudioRecordingController from '../utils/AudioRecordingController.js';
import WAVEncoder from '../utils/WAVEncoder.js';
import IndexedDBController from '../utils/IndexedDBController.js';

class TestPresenter {
  constructor(model) {
    this.view = null;
    this.model = model;
    this.audioController = new AudioRecordingController();
    this.dbController = new IndexedDBController();
    this.recordingTimer = null;
  }

  async init() {
    try {
      // Initialize database
      await this.dbController.initialize();

      // Create view with recording callback
      this.view = new TestView();
      this.view.setRecordingCallback(this.handleRecordingToggle.bind(this));

      this.render();

      // Don't initialize audio immediately - wait for user to click record button
      // This prevents microphone permission request on page load
      this.view.setRecordingAvailable(false); // Show orange "needs permission" state

    } catch (error) {
      console.error('❌ Failed to initialize TestPresenter:', error);
      this.showError('Failed to initialize recording. Please check microphone permissions.');
    }
  }

  async initializeAudio() {
    try {
      await this.audioController.initialize();
      console.log('🎤 Audio recording ready');

      // Update view to show recording is available
      if (this.view) {
        this.view.setRecordingAvailable(true);
      }
    } catch (error) {
      console.error('❌ Audio initialization failed:', error);
      if (this.view) {
        this.view.setRecordingAvailable(false);
        this.view.showError('Microphone access required for recording');
      }
    }
  }

  render() {
    // Clear existing content
    const appElement = document.getElementById('app');
    appElement.innerHTML = '';

    // Render the view
    const viewElement = this.view.render();
    appElement.appendChild(viewElement);
  }

  /**
   * Handle recording toggle (start/stop)
   */
  async handleRecordingToggle() {
    try {
      const isRecording = this.model.isCurrentlyRecording();

      if (isRecording) {
        await this.stopRecording();
      } else {
        // Check if audio is initialized, if not - initialize it now (first time or after cleanup)
        const audioState = this.audioController.getRecordingState();
        if (!audioState.isInitialized) {
          console.log('🔄 Initializing audio (requesting microphone permission)...');
          this.view.showStatus('Requesting microphone permission...');
          await this.initializeAudio();
        }

        await this.startRecording();
      }
    } catch (error) {
      console.error('❌ Recording toggle failed:', error);
      this.showError('Recording failed. Please try again.');
      // Reset button state on error
      this.view.setRecordingAvailable(false);
    }
  }

  /**
   * Start audio recording
   */
  async startRecording() {
    try {
      console.log('🔴 Starting recording...');

      // Update model state
      this.model.setRecording(true);

      // Generate recording name
      const recordingName = this.model.generateRecordingName();
      this.model.setRecordingMetadata({
        name: recordingName,
        category: 'Speaking Test',
        date: new Date().toISOString()
      });

      // Start audio recording
      await this.audioController.startRecording();

      // Update view
      this.view.setRecordingState(true);

      // Start recording timer
      this.startRecordingTimer();

      console.log('🎤 Recording started successfully');

    } catch (error) {
      console.error('❌ Failed to start recording:', error);
      this.model.setRecording(false);
      this.view.setRecordingState(false);
      throw error;
    }
  }

  /**
   * Stop audio recording and save to IndexedDB
   */
  async stopRecording() {
    try {
      console.log('⏹️ Stopping recording...');

      // Stop recording timer
      this.stopRecordingTimer();

      // Stop audio recording (this will automatically release microphone resources)
      const recordingResult = await this.audioController.stopRecording();

      if (!recordingResult) {
        throw new Error('No recording data received');
      }

      // Update model state
      this.model.stopRecording();
      this.model.setCurrentRecordingData({
        audioData: recordingResult.audioData,
        duration: recordingResult.duration,
        sampleRate: recordingResult.sampleRate,
        channels: recordingResult.channels
      });

      // Convert to WAV format
      const wavFile = WAVEncoder.createWAVFile(
        recordingResult.audioData,
        recordingResult.sampleRate,
        recordingResult.channels,
        `${this.model.getRecordingMetadata().name}.wav`
      );

      // Update recording data with WAV blob
      this.model.setCurrentRecordingData({
        audioBlob: wavFile.blob,
        size: wavFile.size
      });

      // Save to IndexedDB
      const recordingId = await this.saveRecording();

      // Update view
      this.view.setRecordingState(false);
      this.view.showRecordingComplete(recordingId, wavFile.duration, wavFile.size);

      // Update view to show microphone is no longer available (since resources are released)
      this.view.setRecordingAvailable(false);

      console.log('✅ Recording completed and saved:', recordingId);
      console.log('🔒 Microphone resources have been released');

    } catch (error) {
      console.error('❌ Failed to stop recording:', error);
      this.model.stopRecording();
      this.view.setRecordingState(false);
      this.view.setRecordingAvailable(false);
      throw error;
    }
  }

  /**
   * Save recording to IndexedDB
   */
  async saveRecording() {
    try {
      const recordingData = this.model.getCurrentRecordingData();
      const metadata = this.model.getRecordingMetadata();

      const recordingId = await this.dbController.saveRecording({
        audioBlob: recordingData.audioBlob,
        name: metadata.name,
        category: metadata.category,
        duration: recordingData.duration,
        sampleRate: recordingData.sampleRate,
        channels: recordingData.channels
      });

      // Add to model's recording history
      this.model.addRecording({
        id: recordingId,
        name: metadata.name,
        category: metadata.category,
        date: metadata.date,
        duration: recordingData.duration,
        size: recordingData.size,
        sampleRate: recordingData.sampleRate,
        channels: recordingData.channels
      });

      // Clear current recording data
      this.model.clearCurrentRecording();

      return recordingId;

    } catch (error) {
      console.error('❌ Failed to save recording:', error);
      throw error;
    }
  }

  /**
   * Start recording timer to update duration
   */
  startRecordingTimer() {
    this.recordingTimer = setInterval(() => {
      const state = this.audioController.getRecordingState();
      this.model.updateRecordingDuration(state.duration);

      // Update view with current duration
      if (this.view) {
        this.view.updateRecordingDuration(state.duration);
      }
    }, 100); // Update every 100ms
  }

  /**
   * Stop recording timer
   */
  stopRecordingTimer() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }

  /**
   * Show error message to user
   */
  showError(message) {
    if (this.view) {
      this.view.showError(message);
    }
  }

  destroy() {
    // Stop recording if active
    if (this.model && this.model.isCurrentlyRecording()) {
      this.stopRecording().catch(console.error);
    }

    // Stop timer
    this.stopRecordingTimer();

    // Clean up audio controller
    if (this.audioController) {
      this.audioController.cleanup();
    }

    // Close database connection
    if (this.dbController) {
      this.dbController.close();
    }

    // Destroy view
    if (this.view) {
      this.view.destroy();
      this.view = null;
    }
  }
}

export default TestPresenter;
